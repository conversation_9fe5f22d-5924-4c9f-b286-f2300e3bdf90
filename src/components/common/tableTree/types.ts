import { VxeTablePropTypes, VxeTableEvents } from 'vxe-table'

export enum TreeNodeType {
  Org = 1,
  Terminal,

  // 写频专用的节点类型
  Folder = 3, // 可展开节点
  Item = 4, // 不可展开节点
}

export type TreeNodeData = {
  rid: string
  parentOrgId: string
  nodeType: TreeNodeType
  children: TreeNodeData[]
}

export type MenuConfig = VxeTablePropTypes.MenuConfig<TreeNodeData>
export type MenuEventHandler = VxeTableEvents.MenuClick<TreeNodeData>
