<template>
  <div class="w-[250px]! h-full">
    <VxeTableTree
      ref="tableTree"
      :source-data="sourceData"
      :shouldSyncSourceData="false"
      :withPageHeader="false"
      :filter="false"
      :enableCheckbox="false"
      :menuConfig="menuConfig"
      @cell-click.prevent="cellClickHandler"
      class="[&_.tree-grid-container]:[border-image:none]! [&_.tree-grid-container]:[border-width:0]!"
    >
      <template #content="{ row }">
        <vxe-column-content :enable-checkbox="false" :row="row" :use-my-tooltip="true">
          <template #default>
            <div class="w-full text-center overflow-hidden overflow-ellipsis">
              <EllipsisText :content="row.title" />
            </div>
          </template>
        </vxe-column-content>
      </template>
    </VxeTableTree>
  </div>
</template>

<script setup lang="ts">
  import { MenuConfig, TreeNodeData, TreeNodeType } from '@/components/common/tableTree'

  const props = withDefaults(
    defineProps<{
      sourceData: TreeNodeData[]
      menuConfig: MenuConfig
    }>(),
    {
      sourceData: () => [],
      menuConfig: () => ({}),
    }
  )

  const emits = defineEmits(['cellClickHandler'])

  const cellClickHandler = () => {
    emits('cellClickHandler')
  }
</script>
